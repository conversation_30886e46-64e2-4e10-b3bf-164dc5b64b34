import { useTranslation } from 'react-i18next';
import type { TranslationKeys } from '@/types/i18n';

interface InterpolationOptions {
  [key: string]: string | number;
}

export const useTypedTranslation = () => {
  const { t, i18n } = useTranslation();

  const typedT = (key: TranslationKeys | string, options?: InterpolationOptions): string => {
    return t(key, options);
  };

  return {
    t: typedT,
    i18n,
  };
};