import React, { useMemo } from 'react';
import { FONTS } from '@constants/fonts';
import { View, StyleSheet, Text } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import CardButton from '@/partials/card-button';
import CardShadowButton from '@/partials/card-shadow-button';
import { StatusBar } from 'react-native';
import ControlButtons from '@src/components/ControlButtons';
import SettingsModal from '@src/components/SettingsModal';
import LanguageBS from '@src/components/LanguageBS';
import AuroraBackground from '@src/components/AuroraBackground';
import ArrowRightBig from '@src/icons/ArrowRightBigIcon';
import { CardPlayGradientBg } from '@src/components/CardPlayGradientBg';
import { useNavigation } from '@react-navigation/native';
import { HomeScreenNavigationProp } from '@/types/navigation';
import { useAppContext } from '@src/context/AppContext';
import ConfirmExitModal from '@src/components/ConfirmExitModal';

export default function GamePlay() {
  const insets = useSafeAreaInsets();
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const { state } = useAppContext();

  const [currentQuestion, setCurrentQuestion] = React.useState(1);

  const [isSettingsModalOpen, setIsSettingsModalOpen] = React.useState(false);
    const [isConfirmExitModalOpen, setIsConfirmExitModalOpen] =
      React.useState(false);

  const question = useMemo(() => {
    if (state.language === 'en') {
      return state.questionsEn.find(item => item.id === currentQuestion);
    }
    return state.questionsVi.find(item => item.id === currentQuestion);
  }, [currentQuestion, state.language, state.questionsEn, state.questionsVi])


  const onNextQuestion = () => {
    setCurrentQuestion(prev => {
      if (prev >= state.questionsEn.length - 1) {
        return 0;
      }
      return prev + 1;
    });
  };

  const onShuffleQuestion = () => {
    setCurrentQuestion(Math.floor(Math.random() * state.questionsEn.length));
  };

    const onHandleGoBack = () => {
    setIsConfirmExitModalOpen(true);
  };

  const onHandleYes = () => {
    setIsConfirmExitModalOpen(false);
    navigation.goBack();
  };

  return (
    <>
      <StatusBar barStyle="light-content" />
      <AuroraBackground>
        <View
          style={[
            styles.container,
            { paddingBottom: insets.bottom, paddingTop: insets.top },
          ]}
        >
          <View style={styles.icons}>
            <ControlButtons
              onOpenSettings={() => {
                setIsSettingsModalOpen(true);
              }}
              onGoBack={onHandleGoBack}
            />
          </View>
          <View style={styles.contentContainer}>
            <CardPlayGradientBg>
              <View style={styles.cardContainer}>
                <Text style={styles.title}>{question!.question}</Text>
                {question!.options && question!.options.length > 0 && (
                  <View style={styles.optionsContainer}>
                    {question!.options.map((item, index) => (
                      <View key={index} style={styles.optionItem}>
                        <Text style={styles.optionItemText}>
                          {item}
                        </Text>
                      </View>
                    ))}
                  </View>
                )}
              </View>
            </CardPlayGradientBg>
          </View>

          <View style={styles.termsContainer}>
            <CardButton
              title="Shuffle"
              containerStyle={{ width: 'auto' }}
              onPress={onShuffleQuestion}
            />
            <CardShadowButton
              onPress={onNextQuestion}
              containerStyle={{ width: 100 }}
            >
              <ArrowRightBig width={24} height={24} />
            </CardShadowButton>
          </View>
        </View>
        <SettingsModal
          visible={isSettingsModalOpen}
          onClose={() => {
            setIsSettingsModalOpen(false);
          }}
        />
        <LanguageBS />
                <ConfirmExitModal
          visible={isConfirmExitModalOpen}
          onClose={() => {
            setIsConfirmExitModalOpen(false);
          }}
          onYes={onHandleYes}
        />
      </AuroraBackground>
    </>
  );
}

const styles = StyleSheet.create({
  blurView: {
    flex: 1,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  container: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  button: {
    backgroundColor: '#2196F3',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    fontFamily: FONTS.CRIMSON_TEXT.REGULAR,
    color: 'rgba(255, 255, 255, 0.85)',
    textAlign: 'center',
  },
  termsContainer: {
    paddingHorizontal: 24,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    textAlign: 'center',
    gap: 20,
  },
  termsLabel: {
    fontSize: 14,
    fontFamily: FONTS.BALOO2.REGULAR,
    color: '#8E7BA1',
    lineHeight: 20,
  },
  infoContainer: {
    gap: 8,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  hidden: {
    width: 'auto',
    opacity: 0,
  },
  icons: {
    width: '100%',
  },
  contentContainer: {
    paddingHorizontal: 8,
    width: '100%',
    height: 'auto',
    minHeight: 512,
  },
  cardContainer: {
    paddingVertical: 36,
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
    gap: 20,
    height: 'auto',
  },
  optionsContainer: {
    justifyContent: 'center',
    alignItems: 'flex-start',
    gap: 4,
  },
  optionItem: {
    width: '100%',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  optionItemText: {
    fontSize: 20,
    fontFamily: FONTS.CRIMSON_TEXT.REGULAR,
    color: '#FFFFFFD9',
    lineHeight: 20,
  },
});
