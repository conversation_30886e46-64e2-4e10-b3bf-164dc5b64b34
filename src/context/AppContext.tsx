import React, {
  createContext,
  useReducer,
  useContext,
  Dispatch,
  useEffect,
} from 'react';
import { AppAction, appReducer, initialState } from './AppReducer';
import { AppState } from '@/types/app-context';
import AsyncStorage from '@react-native-async-storage/async-storage';
import i18n, { STORAGE_KEY } from '@src/i18n';
import { Language } from '@/types/i18n';
import REDUCER from '@constants/reducer';
import { I18nManager } from 'react-native';

type AppContextType = {
  state: AppState;
  dispatch: Dispatch<AppAction>;
  changeLanguage: (language: Language) => Promise<void>;
};

const AppContext = createContext<AppContextType>({
  state: initialState,
  dispatch: () => null, // Placeholder function
});

export const AppProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  useEffect(() => {
    loadSavedLanguage();
  }, []);

  const loadSavedLanguage = async (): Promise<void> => {
    try {
      const savedLanguage = await AsyncStorage.getItem(STORAGE_KEY);
      if (savedLanguage && (savedLanguage === 'vi' || savedLanguage === 'en')) {
        const language = savedLanguage as Language;
        dispatch({ type: REDUCER.SET_LANGUAGE, payload: language });
        await i18n.changeLanguage(language);
      }
    } catch (error) {
      console.warn('Error loading language:', error);
    } finally {
    }
  };

  const changeLanguage = async (language: Language): Promise<void> => {
    try {
      // Handle RTL languages if needed
      const isRTL = ['ar', 'he'].includes(language);
      if (I18nManager.isRTL !== isRTL) {
        I18nManager.allowRTL(isRTL);
        I18nManager.forceRTL(isRTL);
        // Note: App restart may be needed for RTL changes
      }

      await AsyncStorage.setItem(STORAGE_KEY, language);
      dispatch({ type: REDUCER.SET_LANGUAGE, payload: language });
      await i18n.changeLanguage(language);
    } catch (error) {
      console.error('Error saving language:', error);
    }
  };

  return (
    <AppContext.Provider value={{ state, dispatch, changeLanguage }}>
      {children}
    </AppContext.Provider>
  );
};

// Custom hook để truy cập Context
export const useAppContext = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within AppProvider');
  }
  return context;
};
