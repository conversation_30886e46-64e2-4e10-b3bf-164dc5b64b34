/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */

import { StatusBar, StyleSheet, useColorScheme } from 'react-native';
import {
  DarkTheme,
  DefaultTheme,
  NavigationContainer,
  ThemeProvider,
} from '@react-navigation/native';

import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { createStackNavigator } from '@react-navigation/stack';
import HomeScreen from './src/screens/Home';
import NotFoundScreen from './src/screens/NotFound';
import type { RootStackParamList } from '@/types/navigation';
// import GamePlay from '@src/screens/GamePlay';
// import GamePlayMulti from '@src/screens/GamePlayMulti';
import { AppProvider } from '@src/context/AppContext';
import BaseGamePlay from '@src/screens/BaseGamePlay';

const Stack = createStackNavigator<RootStackParamList>();

function App() {
  const isDarkMode = useColorScheme() === 'dark';
  const colorScheme = useColorScheme();

  return (
    <SafeAreaProvider>
      <GestureHandlerRootView style={styles.container}>
        <AppProvider>
          <ThemeProvider
            value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}
          >
            <NavigationContainer>
              <Stack.Navigator
                screenOptions={{ headerShown: false }}
                initialRouteName="Home"
              >
                <Stack.Screen name="Home" component={HomeScreen} />
                {/* <Stack.Screen name="GamePlay" component={GamePlay} />
                <Stack.Screen name="GamePlayMulti" component={GamePlayMulti} /> */}
                <Stack.Screen name="BaseGamePlay" component={BaseGamePlay} />
                <Stack.Screen name="NotFound" component={NotFoundScreen} />
              </Stack.Navigator>
            </NavigationContainer>
            <StatusBar
              barStyle={isDarkMode ? 'light-content' : 'dark-content'}
            />
          </ThemeProvider>
        </AppProvider>
      </GestureHandlerRootView>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default App;
