import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import AsyncStorage from '@react-native-async-storage/async-storage';


import vi from './locales/vi.json';
import en from './locales/en.json';

import type { Language } from '@/types/i18n';

export const STORAGE_KEY = 'user-language';
export const DEFAULT_LANGUAGE: Language = 'vi';
export const FALLBACK_LANGUAGE: Language = 'en';

const resources = {
  vi: { translation: vi },
  en: { translation: en },
} as const;

const initI18n = async (): Promise<void> => {
  let savedLanguage: Language = DEFAULT_LANGUAGE;
  
  try {
    const stored = await AsyncStorage.getItem(STORAGE_KEY);
    if (stored && (stored === 'vi' || stored === 'en')) {
      savedLanguage = stored as Language;
    }
  } catch (error) {
    console.warn('Failed to load saved language:', error);
  }

  await i18n
    .use(initReactI18next)
    .init({
      resources,
      lng: savedLanguage,
      fallbackLng: FALLBACK_LANGUAGE,
      interpolation: {
        escapeValue: false,
      },
      react: {
        useSuspense: false,
      },
    });
};

export { initI18n };
export default i18n;