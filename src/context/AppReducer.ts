import { AppState, Question } from '@/types/app-context';
import REDUCER from '@constants/reducer';

export const initialState: AppState = {
  theme: 'light',
  language: 'en',
  faceToFaceMode: true,
  questionsEn: [],
  questionsVi: [],
};

export type AppAction =
  | { type: typeof REDUCER.SET_LANGUAGE; payload: 'en' | 'vi' }
  | { type: typeof REDUCER.TOGGLE_FACE_TO_FACE_MODE }
  | { type: typeof REDUCER.TOGGLE_THEME }
  | {
      type: typeof REDUCER.SET_QUESTIONS;
      payload: {
        questionsEn: Question[];
        questionsVi: Question[];
      };
    };

// Reducer function với type annotation
export const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case REDUCER.TOGGLE_THEME:
      return { ...state, theme: state.theme === 'light' ? 'dark' : 'light' };
    case REDUCER.SET_LANGUAGE:
      return { ...state, language: action.payload };
    case REDUCER.TOGGLE_FACE_TO_FACE_MODE:
      return { ...state, faceToFaceMode: !state.faceToFaceMode };
    case REDUCER.SET_QUESTIONS:
      return {
        ...state,
        questionsEn: action.payload.questionsEn,
        questionsVi: action.payload.questionsVi,
      };
    default:
      return state;
  }
};
