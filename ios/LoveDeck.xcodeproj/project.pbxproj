// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		154A3D0012DF44B997CC397E /* Baloo2-ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 2301F928C8044B75A939C86C /* Baloo2-ExtraBold.ttf */; };
		2EEB3E7199534161A615D323 /* favicon.png in Resources */ = {isa = PBXBuildFile; fileRef = F4599185FDA9457188818F77 /* favicon.png */; };
		34654E9E94C443D1882D363D /* Imbue-400Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 96294C2D69BD471F8758DEA1 /* Imbue-400Regular.ttf */; };
		4B9399E637D8431F8B185EF6 /* Imbue-800ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 78428AC4F79D4B9CB9A2BBEA /* Imbue-800ExtraBold.ttf */; };
		57D83C232E295C7600D9C23A /* heart_brigde_v2.riv in Resources */ = {isa = PBXBuildFile; fileRef = 57D83C222E295C7600D9C23A /* heart_brigde_v2.riv */; };
		57F1C3242E2030FC009D9969 /* heart_brigde.riv in Resources */ = {isa = PBXBuildFile; fileRef = 57F1C3232E2030FC009D9969 /* heart_brigde.riv */; };
		63D74EFD465B4FDCA60F22D0 /* Imbue-600SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = EBC2897A87E344AFA2E62FC7 /* Imbue-600SemiBold.ttf */; };
		69D6DFA9344C4C12AE061CBF /* icon.png in Resources */ = {isa = PBXBuildFile; fileRef = BD4774F0038B49229905BFA4 /* icon.png */; };
		6FD1EA70367342278D09A501 /* Imbue-500Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 12E40C30EEF84456B39CBA9B /* Imbue-500Medium.ttf */; };
		761780ED2CA45674006654EE /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 761780EC2CA45674006654EE /* AppDelegate.swift */; };
		7D44AF78B7B1426A92A525AF /* fire.png in Resources */ = {isa = PBXBuildFile; fileRef = A652AA9BCACD4D9E856BAB09 /* fire.png */; };
		7F510774C86B4CFCB27B55BA /* Imbue-900Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B881E7832F584650A968A12A /* Imbue-900Black.ttf */; };
		81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		86EF508C3A1B47E7A73EFE8E /* Baloo2-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 5B18D8E7A1104A2AB429A63C /* Baloo2-SemiBold.ttf */; };
		8F9CB7BB0AF9437898645592 /* Baloo2-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 15B0E9E4C7CE418E8B96A67E /* Baloo2-Medium.ttf */; };
		92AC5C55B1A86D227680A240 /* libPods-loveDeck.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 7E440223F995CB2C86B68C12 /* libPods-loveDeck.a */; };
		9FF0512036AA473CB43B5CEA /* Imbue-700Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9B45A742B7844A2CB018D57F /* Imbue-700Bold.ttf */; };
		A12D1DEE1C4743E7849A8381 /* Baloo2-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C3BFE1DB36054884BDBA157D /* Baloo2-Bold.ttf */; };
		BAC019FF017F73AE0A14E30C /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */; };
		DB6C21EF71644C2FAE81E6BE /* Baloo2-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 98276CB9556149CDBCB5B07D /* Baloo2-Regular.ttf */; };
		6B14EBB65EBA4D499CCC0F79 /* CrimsonText-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 939139D2AD954DA183F0981B /* CrimsonText-Bold.ttf */; };
		E9FF0884124A4B129DBF5E06 /* CrimsonText-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 47E376EB54EE4D59B54B6C04 /* CrimsonText-BoldItalic.ttf */; };
		0F072297C1AC4F6391AF434B /* CrimsonText-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BA46616069A64EE7A3BCE297 /* CrimsonText-Italic.ttf */; };
		EB58286E101F4DB4B4628302 /* CrimsonText-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E2EA36CB847B4BDB8F0520B0 /* CrimsonText-Regular.ttf */; };
		DA7B671DA4724985ACC0A60A /* CrimsonText-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CC7EBC5303434E1CB7002C46 /* CrimsonText-SemiBold.ttf */; };
		11192E2EF5A44496AD225C2A /* CrimsonText-SemiBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 4ECDE4E4692841D28004FCE2 /* CrimsonText-SemiBoldItalic.ttf */; };
		38C59949BC6D4412A3C3A847 /* clock.png in Resources */ = {isa = PBXBuildFile; fileRef = 9C0A8F6B372C4C4C95D075C2 /* clock.png */; };
		E90518C6C634448389A01333 /* lock.png in Resources */ = {isa = PBXBuildFile; fileRef = 86E1F41CDE2E48798F55C4FF /* lock.png */; };
		AB6245D00D7346FF8E630AB5 /* replay.png in Resources */ = {isa = PBXBuildFile; fileRef = 2C0BD71CE48C48C882AB30FE /* replay.png */; };
		2EC46E1F905D4E6CADEE4786 /* adaptive_icon.png in Resources */ = {isa = PBXBuildFile; fileRef = A91E29E5975941D9AA08D03A /* adaptive_icon.png */; };
		7740EB2E92F04E8782F6C01E /* bg_crossing_lines.png in Resources */ = {isa = PBXBuildFile; fileRef = 10223A7208F24DC7AB4F091E /* bg_crossing_lines.png */; };
		E0C27D3D7C3F4903940AEBBB /* bg_ember_afterglow.png in Resources */ = {isa = PBXBuildFile; fileRef = A3F2BA5E7DE941C18556B981 /* bg_ember_afterglow.png */; };
		FE9578BDBF9940EEB9DA6F86 /* bg_first_date.png in Resources */ = {isa = PBXBuildFile; fileRef = B21F5693948F42ACAD253165 /* bg_first_date.png */; };
		AB6277A88A914FE7A9A49270 /* bg_late_night_talk.png in Resources */ = {isa = PBXBuildFile; fileRef = 912E9E8C2C384F2DBA6C15BE /* bg_late_night_talk.png */; };
		926AE4506DBC4435BB9BA0E1 /* ear_phone.png in Resources */ = {isa = PBXBuildFile; fileRef = C013682068A74C94B748DC31 /* ear_phone.png */; };
		37F9275D0123410DAF93F738 /* home_bg.png in Resources */ = {isa = PBXBuildFile; fileRef = 449511FA6EC04C8DBACF7E77 /* home_bg.png */; };
		894C0B09C45147E08940EAC4 /* money_fly.png in Resources */ = {isa = PBXBuildFile; fileRef = A0986303DD324572AEECB723 /* money_fly.png */; };
		A524F8D313B94A109123B233 /* splash_icon.png in Resources */ = {isa = PBXBuildFile; fileRef = A3147DE193B34A479280BD44 /* splash_icon.png */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		12E40C30EEF84456B39CBA9B /* Imbue-500Medium.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Imbue-500Medium.ttf"; path = "../assets/fonts/Imbue-500Medium.ttf"; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* loveDeck.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = loveDeck.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = loveDeck/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = loveDeck/Info.plist; sourceTree = "<group>"; };
		13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = PrivacyInfo.xcprivacy; path = loveDeck/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		15B0E9E4C7CE418E8B96A67E /* Baloo2-Medium.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Baloo2-Medium.ttf"; path = "../assets/fonts/Baloo2-Medium.ttf"; sourceTree = "<group>"; };
		2301F928C8044B75A939C86C /* Baloo2-ExtraBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Baloo2-ExtraBold.ttf"; path = "../assets/fonts/Baloo2-ExtraBold.ttf"; sourceTree = "<group>"; };
		57D83C222E295C7600D9C23A /* heart_brigde_v2.riv */ = {isa = PBXFileReference; lastKnownFileType = file; path = heart_brigde_v2.riv; sourceTree = "<group>"; };
		57F1C3232E2030FC009D9969 /* heart_brigde.riv */ = {isa = PBXFileReference; lastKnownFileType = file; path = heart_brigde.riv; sourceTree = "<group>"; };
		5B18D8E7A1104A2AB429A63C /* Baloo2-SemiBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Baloo2-SemiBold.ttf"; path = "../assets/fonts/Baloo2-SemiBold.ttf"; sourceTree = "<group>"; };
		761780EC2CA45674006654EE /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; name = AppDelegate.swift; path = loveDeck/AppDelegate.swift; sourceTree = "<group>"; };
		78428AC4F79D4B9CB9A2BBEA /* Imbue-800ExtraBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Imbue-800ExtraBold.ttf"; path = "../assets/fonts/Imbue-800ExtraBold.ttf"; sourceTree = "<group>"; };
		7E440223F995CB2C86B68C12 /* libPods-loveDeck.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-loveDeck.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = loveDeck/LaunchScreen.storyboard; sourceTree = "<group>"; };
		96294C2D69BD471F8758DEA1 /* Imbue-400Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Imbue-400Regular.ttf"; path = "../assets/fonts/Imbue-400Regular.ttf"; sourceTree = "<group>"; };
		97CAD17DF063333950FCB651 /* Pods-loveDeck.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-loveDeck.debug.xcconfig"; path = "Target Support Files/Pods-loveDeck/Pods-loveDeck.debug.xcconfig"; sourceTree = "<group>"; };
		98276CB9556149CDBCB5B07D /* Baloo2-Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Baloo2-Regular.ttf"; path = "../assets/fonts/Baloo2-Regular.ttf"; sourceTree = "<group>"; };
		9B45A742B7844A2CB018D57F /* Imbue-700Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Imbue-700Bold.ttf"; path = "../assets/fonts/Imbue-700Bold.ttf"; sourceTree = "<group>"; };
		A652AA9BCACD4D9E856BAB09 /* fire.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = fire.png; path = ../assets/images/fire.png; sourceTree = "<group>"; };
		AC2DA17EA6DF95E4E31DEB4B /* Pods-loveDeck.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-loveDeck.release.xcconfig"; path = "Target Support Files/Pods-loveDeck/Pods-loveDeck.release.xcconfig"; sourceTree = "<group>"; };
		B881E7832F584650A968A12A /* Imbue-900Black.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Imbue-900Black.ttf"; path = "../assets/fonts/Imbue-900Black.ttf"; sourceTree = "<group>"; };
		BD4774F0038B49229905BFA4 /* icon.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = icon.png; path = ../assets/images/icon.png; sourceTree = "<group>"; };
		C3BFE1DB36054884BDBA157D /* Baloo2-Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Baloo2-Bold.ttf"; path = "../assets/fonts/Baloo2-Bold.ttf"; sourceTree = "<group>"; };
		EBC2897A87E344AFA2E62FC7 /* Imbue-600SemiBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Imbue-600SemiBold.ttf"; path = "../assets/fonts/Imbue-600SemiBold.ttf"; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		F4599185FDA9457188818F77 /* favicon.png */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = favicon.png; path = ../assets/images/favicon.png; sourceTree = "<group>"; };
		939139D2AD954DA183F0981B /* CrimsonText-Bold.ttf */ = {isa = PBXFileReference; name = "CrimsonText-Bold.ttf"; path = "../assets/fonts/CrimsonText-Bold.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		47E376EB54EE4D59B54B6C04 /* CrimsonText-BoldItalic.ttf */ = {isa = PBXFileReference; name = "CrimsonText-BoldItalic.ttf"; path = "../assets/fonts/CrimsonText-BoldItalic.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		BA46616069A64EE7A3BCE297 /* CrimsonText-Italic.ttf */ = {isa = PBXFileReference; name = "CrimsonText-Italic.ttf"; path = "../assets/fonts/CrimsonText-Italic.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		E2EA36CB847B4BDB8F0520B0 /* CrimsonText-Regular.ttf */ = {isa = PBXFileReference; name = "CrimsonText-Regular.ttf"; path = "../assets/fonts/CrimsonText-Regular.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		CC7EBC5303434E1CB7002C46 /* CrimsonText-SemiBold.ttf */ = {isa = PBXFileReference; name = "CrimsonText-SemiBold.ttf"; path = "../assets/fonts/CrimsonText-SemiBold.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		4ECDE4E4692841D28004FCE2 /* CrimsonText-SemiBoldItalic.ttf */ = {isa = PBXFileReference; name = "CrimsonText-SemiBoldItalic.ttf"; path = "../assets/fonts/CrimsonText-SemiBoldItalic.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		9C0A8F6B372C4C4C95D075C2 /* clock.png */ = {isa = PBXFileReference; name = "clock.png"; path = "../assets/images/clock.png"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		86E1F41CDE2E48798F55C4FF /* lock.png */ = {isa = PBXFileReference; name = "lock.png"; path = "../assets/images/lock.png"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		2C0BD71CE48C48C882AB30FE /* replay.png */ = {isa = PBXFileReference; name = "replay.png"; path = "../assets/images/replay.png"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		A91E29E5975941D9AA08D03A /* adaptive_icon.png */ = {isa = PBXFileReference; name = "adaptive_icon.png"; path = "../assets/images/adaptive_icon.png"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		10223A7208F24DC7AB4F091E /* bg_crossing_lines.png */ = {isa = PBXFileReference; name = "bg_crossing_lines.png"; path = "../assets/images/bg_crossing_lines.png"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		A3F2BA5E7DE941C18556B981 /* bg_ember_afterglow.png */ = {isa = PBXFileReference; name = "bg_ember_afterglow.png"; path = "../assets/images/bg_ember_afterglow.png"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		B21F5693948F42ACAD253165 /* bg_first_date.png */ = {isa = PBXFileReference; name = "bg_first_date.png"; path = "../assets/images/bg_first_date.png"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		912E9E8C2C384F2DBA6C15BE /* bg_late_night_talk.png */ = {isa = PBXFileReference; name = "bg_late_night_talk.png"; path = "../assets/images/bg_late_night_talk.png"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		C013682068A74C94B748DC31 /* ear_phone.png */ = {isa = PBXFileReference; name = "ear_phone.png"; path = "../assets/images/ear_phone.png"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		449511FA6EC04C8DBACF7E77 /* home_bg.png */ = {isa = PBXFileReference; name = "home_bg.png"; path = "../assets/images/home_bg.png"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		A0986303DD324572AEECB723 /* money_fly.png */ = {isa = PBXFileReference; name = "money_fly.png"; path = "../assets/images/money_fly.png"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		A3147DE193B34A479280BD44 /* splash_icon.png */ = {isa = PBXFileReference; name = "splash_icon.png"; path = "../assets/images/splash_icon.png"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				92AC5C55B1A86D227680A240 /* libPods-loveDeck.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		13B07FAE1A68108700A75B9A /* loveDeck */ = {
			isa = PBXGroup;
			children = (
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				761780EC2CA45674006654EE /* AppDelegate.swift */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */,
				13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */,
			);
			name = loveDeck;
			sourceTree = "<group>";
		};
		2393D123570241998CB4587C /* Resources */ = {
			isa = PBXGroup;
			children = (
				F4599185FDA9457188818F77 /* favicon.png */,
				A652AA9BCACD4D9E856BAB09 /* fire.png */,
				BD4774F0038B49229905BFA4 /* icon.png */,
				C3BFE1DB36054884BDBA157D /* Baloo2-Bold.ttf */,
				2301F928C8044B75A939C86C /* Baloo2-ExtraBold.ttf */,
				15B0E9E4C7CE418E8B96A67E /* Baloo2-Medium.ttf */,
				98276CB9556149CDBCB5B07D /* Baloo2-Regular.ttf */,
				5B18D8E7A1104A2AB429A63C /* Baloo2-SemiBold.ttf */,
				96294C2D69BD471F8758DEA1 /* Imbue-400Regular.ttf */,
				12E40C30EEF84456B39CBA9B /* Imbue-500Medium.ttf */,
				EBC2897A87E344AFA2E62FC7 /* Imbue-600SemiBold.ttf */,
				9B45A742B7844A2CB018D57F /* Imbue-700Bold.ttf */,
				78428AC4F79D4B9CB9A2BBEA /* Imbue-800ExtraBold.ttf */,
				B881E7832F584650A968A12A /* Imbue-900Black.ttf */,
				939139D2AD954DA183F0981B /* CrimsonText-Bold.ttf */,
				47E376EB54EE4D59B54B6C04 /* CrimsonText-BoldItalic.ttf */,
				BA46616069A64EE7A3BCE297 /* CrimsonText-Italic.ttf */,
				E2EA36CB847B4BDB8F0520B0 /* CrimsonText-Regular.ttf */,
				CC7EBC5303434E1CB7002C46 /* CrimsonText-SemiBold.ttf */,
				4ECDE4E4692841D28004FCE2 /* CrimsonText-SemiBoldItalic.ttf */,
				9C0A8F6B372C4C4C95D075C2 /* clock.png */,
				86E1F41CDE2E48798F55C4FF /* lock.png */,
				2C0BD71CE48C48C882AB30FE /* replay.png */,
				A91E29E5975941D9AA08D03A /* adaptive_icon.png */,
				10223A7208F24DC7AB4F091E /* bg_crossing_lines.png */,
				A3F2BA5E7DE941C18556B981 /* bg_ember_afterglow.png */,
				B21F5693948F42ACAD253165 /* bg_first_date.png */,
				912E9E8C2C384F2DBA6C15BE /* bg_late_night_talk.png */,
				C013682068A74C94B748DC31 /* ear_phone.png */,
				449511FA6EC04C8DBACF7E77 /* home_bg.png */,
				A0986303DD324572AEECB723 /* money_fly.png */,
				A3147DE193B34A479280BD44 /* splash_icon.png */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				7E440223F995CB2C86B68C12 /* libPods-loveDeck.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		57F1C3222E2030CD009D9969 /* Assets */ = {
			isa = PBXGroup;
			children = (
				57D83C222E295C7600D9C23A /* heart_brigde_v2.riv */,
				57F1C3232E2030FC009D9969 /* heart_brigde.riv */,
			);
			path = Assets;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				57F1C3222E2030CD009D9969 /* Assets */,
				13B07FAE1A68108700A75B9A /* loveDeck */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
				2393D123570241998CB4587C /* Resources */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* loveDeck.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				97CAD17DF063333950FCB651 /* Pods-loveDeck.debug.xcconfig */,
				AC2DA17EA6DF95E4E31DEB4B /* Pods-loveDeck.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		13B07F861A680F5B00A75B9A /* loveDeck */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "loveDeck" */;
			buildPhases = (
				26CA1BFA3DC44470B12B5C15 /* [CP] Check Pods Manifest.lock */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				F23FBB501ED654556FAC6974 /* [CP] Embed Pods Frameworks */,
				2AC2CDCB332636B11A08E40A /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = loveDeck;
			productName = loveDeck;
			productReference = 13B07F961A680F5B00A75B9A /* loveDeck.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1210;
				TargetAttributes = {
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1120;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "loveDeck" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* loveDeck */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				BAC019FF017F73AE0A14E30C /* PrivacyInfo.xcprivacy in Resources */,
				57F1C3242E2030FC009D9969 /* heart_brigde.riv in Resources */,
				2EEB3E7199534161A615D323 /* favicon.png in Resources */,
				7D44AF78B7B1426A92A525AF /* fire.png in Resources */,
				57D83C232E295C7600D9C23A /* heart_brigde_v2.riv in Resources */,
				69D6DFA9344C4C12AE061CBF /* icon.png in Resources */,
				A12D1DEE1C4743E7849A8381 /* Baloo2-Bold.ttf in Resources */,
				154A3D0012DF44B997CC397E /* Baloo2-ExtraBold.ttf in Resources */,
				8F9CB7BB0AF9437898645592 /* Baloo2-Medium.ttf in Resources */,
				DB6C21EF71644C2FAE81E6BE /* Baloo2-Regular.ttf in Resources */,
				86EF508C3A1B47E7A73EFE8E /* Baloo2-SemiBold.ttf in Resources */,
				34654E9E94C443D1882D363D /* Imbue-400Regular.ttf in Resources */,
				6FD1EA70367342278D09A501 /* Imbue-500Medium.ttf in Resources */,
				63D74EFD465B4FDCA60F22D0 /* Imbue-600SemiBold.ttf in Resources */,
				9FF0512036AA473CB43B5CEA /* Imbue-700Bold.ttf in Resources */,
				4B9399E637D8431F8B185EF6 /* Imbue-800ExtraBold.ttf in Resources */,
				7F510774C86B4CFCB27B55BA /* Imbue-900Black.ttf in Resources */,
				6B14EBB65EBA4D499CCC0F79 /* CrimsonText-Bold.ttf in Resources */,
				E9FF0884124A4B129DBF5E06 /* CrimsonText-BoldItalic.ttf in Resources */,
				0F072297C1AC4F6391AF434B /* CrimsonText-Italic.ttf in Resources */,
				EB58286E101F4DB4B4628302 /* CrimsonText-Regular.ttf in Resources */,
				DA7B671DA4724985ACC0A60A /* CrimsonText-SemiBold.ttf in Resources */,
				11192E2EF5A44496AD225C2A /* CrimsonText-SemiBoldItalic.ttf in Resources */,
				38C59949BC6D4412A3C3A847 /* clock.png in Resources */,
				E90518C6C634448389A01333 /* lock.png in Resources */,
				AB6245D00D7346FF8E630AB5 /* replay.png in Resources */,
				2EC46E1F905D4E6CADEE4786 /* adaptive_icon.png in Resources */,
				7740EB2E92F04E8782F6C01E /* bg_crossing_lines.png in Resources */,
				E0C27D3D7C3F4903940AEBBB /* bg_ember_afterglow.png in Resources */,
				FE9578BDBF9940EEB9DA6F86 /* bg_first_date.png in Resources */,
				AB6277A88A914FE7A9A49270 /* bg_late_night_talk.png in Resources */,
				926AE4506DBC4435BB9BA0E1 /* ear_phone.png in Resources */,
				37F9275D0123410DAF93F738 /* home_bg.png in Resources */,
				894C0B09C45147E08940EAC4 /* money_fly.png in Resources */,
				A524F8D313B94A109123B233 /* splash_icon.png in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"$REACT_NATIVE_PATH/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"$REACT_NATIVE_PATH/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		26CA1BFA3DC44470B12B5C15 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-loveDeck-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		2AC2CDCB332636B11A08E40A /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-loveDeck/Pods-loveDeck-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-loveDeck/Pods-loveDeck-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-loveDeck/Pods-loveDeck-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		F23FBB501ED654556FAC6974 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-loveDeck/Pods-loveDeck-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-loveDeck/Pods-loveDeck-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-loveDeck/Pods-loveDeck-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				761780ED2CA45674006654EE /* AppDelegate.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 97CAD17DF063333950FCB651 /* Pods-loveDeck.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 94A92FWC5J;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = loveDeck/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.nguyenqui1011.loveDeck;
				PRODUCT_NAME = loveDeck;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AC2DA17EA6DF95E4E31DEB4B /* Pods-loveDeck.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 94A92FWC5J;
				INFOPLIST_FILE = loveDeck/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.nguyenqui1011.loveDeck;
				PRODUCT_NAME = loveDeck;
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = "$(inherited)  ";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) DEBUG";
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = "$(inherited)  ";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "loveDeck" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "loveDeck" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
